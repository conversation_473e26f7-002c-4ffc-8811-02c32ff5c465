package dataset

import (
	"fmt"

	"github.com/berrijam/mulberri/internal/utils/logger"
)

// ====================
// DatasetView Structure (Subset Operations)
// ====================

// FeatureStats provides statistical summary for a feature within a DatasetView.
//
// Purpose: Aggregates key statistics for feature analysis and split evaluation.
// Contains both categorical (unique values, mode) and numerical (min, max, mean) statistics.
//
// Design: Unified structure supporting all feature types with optional numerical fields.
// - Count: Total non-missing values in the view
// - UniqueCount: Number of distinct values
// - Mode: Most frequent value (nil if tie or no values)
// - Min/Max/Mean: Only populated for numerical features (nil for categorical)
//
// Constraints: Numerical fields only valid when feature IsNumerical() returns true.
// Performance: Calculated on-demand, not cached (use GetFeatureDistribution for caching).
// Relationships: Derived from feature distributions and column numerical values.
// Side effects: None, read-only statistical calculations.
//
// Example:
//
//	stats, err := view.GetFeatureStatistics("age")
//	// stats.Count = 100, stats.UniqueCount = 25, stats.Min = 18.0, stats.Max = 65.0
type FeatureStats struct {
	Count       int      // Total non-missing values in view
	UniqueCount int      // Number of distinct values
	Mode        any      // Most frequent value (nil if tie)
	ModeCount   int      // Count of mode value
	Min         *float64 // Minimum value (numerical features only)
	Max         *float64 // Maximum value (numerical features only)
	Mean        *float64 // Average value (numerical features only)
}

// DatasetView represents an efficient subset view of a parent Dataset.
//
// Purpose: Provides access to a subset of dataset rows without data duplication.
// Uses logical indexing (0 to size-1) that maps to physical indices in the parent
// dataset. Essential for decision tree algorithms that need to work with data splits.
//
// Design Pattern: Reference-based view with cached calculations
// - Maintains reference to parent dataset (no data copying)
// - Stores only active row indices and cached computations
// - Logical indices in view map to physical indices in parent dataset
//
// Constraints:
// - activeIndices must contain valid physical indices from parent dataset
// - Logical indices must be in range [0, size)
// - View is immutable after creation (indices cannot be modified)
//
// Security: No sensitive data storage, inherits parent dataset's data access.
// Performance: O(1) creation, O(1) feature access, O(n) target distribution calculation.
// Relationships: References parent Dataset, can create child DatasetViews.
// Side effects: Caches target distribution, marks cache dirty on creation.
//
// Example:
//
//	view := dataset.CreateView([]int{0, 2, 4}) // physical indices 0, 2, 4
//	value, err := view.GetFeatureValue(0, "age") // logical index 0 -> physical index 0
//	childView := view.CreateChildView([]int{0, 2}) // logical indices 0, 2 -> physical indices 0, 4
type DatasetView[T comparable] struct {
	dataset       *Dataset[T] // Reference to full dataset
	activeIndices []int       // Which rows from full dataset are active
	size          int         // Number of active rows

	// Cached calculations for this view
	targetDist      map[T]int // Cached target distribution
	targetDistDirty bool      // Whether cache needs refresh

	// Feature distribution caching
	featureDists      map[string]map[any]int // Cached feature distributions by feature name
	featureDistsDirty map[string]bool        // Per-feature cache dirty flags
}

// ====================
// DatasetView Methods (Subset Operations)
// ====================

// GetFeatureValue retrieves feature value using logical indexing within this view.
//
// Args:
// - logicalIndex: Position within view (0-based, must be < view.size)
// - featureName: Name of feature column (must exist in parent dataset)
//
// Returns: Raw feature value (type depends on column type) or error.
// Constraints: logicalIndex in range [0, size), featureName must exist.
// Performance: O(1) index translation + O(1) column access.
// Relationships: Translates logical index to physical index in parent dataset.
// Side effects: None, read-only operation.
//
// Index Translation: logicalIndex -> activeIndices[logicalIndex] -> column.GetValue()
// Example: view.GetFeatureValue(0, "age") // first row in view, not first row in dataset
func (v *DatasetView[T]) GetFeatureValue(logicalIndex int, featureName string) (any, error) {
	if logicalIndex < 0 || logicalIndex >= v.size {
		logger.Error(fmt.Sprintf("logical index %d out of bounds [0,%d)", logicalIndex, v.size))
		return nil, fmt.Errorf("logical index %d out of bounds [0,%d)", logicalIndex, v.size)
	}

	// Convert logical index to physical index in full dataset
	physicalIndex := v.activeIndices[logicalIndex]

	column, err := v.dataset.GetColumn(featureName)
	if err != nil {
		return nil, err
	}

	return column.GetValue(physicalIndex)
}

// GetTarget retrieves target value using logical indexing within this view.
//
// Args:
// - logicalIndex: Position within view (0-based, must be < view.size)
//
// Returns: Target value of generic type T, or zero value + error if out of bounds.
// Constraints: logicalIndex in range [0, size).
// Performance: O(1) index translation + O(1) array access.
// Relationships: Accesses parent dataset's target array via physical index.
// Side effects: None, read-only operation.
//
// Example: target, err := view.GetTarget(0) // target for first row in view
func (v *DatasetView[T]) GetTarget(logicalIndex int) (T, error) {
	if logicalIndex < 0 || logicalIndex >= v.size {
		var zero T
		logger.Error(fmt.Sprintf("logical index %d out of bounds [0,%d)", logicalIndex, v.size))
		return zero, fmt.Errorf("logical index %d out of bounds [0,%d)", logicalIndex, v.size)
	}

	physicalIndex := v.activeIndices[logicalIndex]
	return v.dataset.targetValues[physicalIndex], nil
}

// GetTargetDistribution calculates target value distribution with caching.
//
// Returns: Map of target values to their occurrence counts within this view.
// Constraints: Only counts targets for rows included in this view's activeIndices.
// Performance: O(n) first call for calculation, O(1) subsequent calls (cached).
// Relationships: Accesses parent dataset's target array, caches result in view.
// Side effects: Updates targetDist cache, sets targetDistDirty to false.
//
// Caching Behavior:
// - First call: Iterates activeIndices, calculates distribution, caches result
// - Subsequent calls: Returns cached result without recalculation
// - Cache marked dirty on view creation, clean after first calculation
//
// Example: dist, err := view.GetTargetDistribution() // {"A": 3, "B": 2}
func (v *DatasetView[T]) GetTargetDistribution() (map[T]int, error) {
	if !v.targetDistDirty && v.targetDist != nil {
		return v.targetDist, nil
	}

	distribution := make(map[T]int)

	// Only iterate over active indices
	for _, physicalIndex := range v.activeIndices {
		target := v.dataset.targetValues[physicalIndex]
		distribution[target]++
	}

	v.targetDist = distribution
	v.targetDistDirty = false
	return distribution, nil
}

// GetFeatureDistribution calculates feature value distribution with caching.
//
// Args:
// - featureName: Name of feature column (must exist in parent dataset)
//
// Returns: Map of feature values to their occurrence counts within this view.
// Constraints: Only counts feature values for rows included in this view's activeIndices.
// Performance: O(n) first call for calculation, O(1) subsequent calls (cached).
// Relationships: Accesses parent dataset's feature columns, caches result in view.
// Side effects: Updates featureDists cache, sets featureDistsDirty[featureName] to false.
//
// Caching Behavior:
// - First call: Iterates activeIndices, calculates distribution, caches result
// - Subsequent calls: Returns cached result without recalculation
// - Cache marked dirty on view creation, clean after first calculation
//
// Example: dist, err := view.GetFeatureDistribution("age") // {25: 2, 30: 1, 35: 3}
func (v *DatasetView[T]) GetFeatureDistribution(featureName string) (map[any]int, error) {
	// Check if we have a cached result that's not dirty
	if !v.featureDistsDirty[featureName] && v.featureDists[featureName] != nil {
		return v.featureDists[featureName], nil
	}

	// Get the column for this feature
	column, err := v.dataset.GetColumn(featureName)
	if err != nil {
		return nil, err
	}

	distribution := make(map[any]int)

	// Only iterate over active indices
	for _, physicalIndex := range v.activeIndices {
		value, err := column.GetValue(physicalIndex)
		if err != nil {
			// Skip missing values - they don't contribute to distribution
			continue
		}
		distribution[value]++
	}

	// Cache the result
	v.featureDists[featureName] = distribution
	v.featureDistsDirty[featureName] = false
	return distribution, nil
}

// GetFeatureStatistics calculates comprehensive statistics for a feature within this view.
//
// Args:
// - featureName: Name of feature column (must exist in parent dataset)
//
// Returns: FeatureStats with count, unique values, mode, and numerical stats (if applicable).
// Constraints: Only counts non-missing values for rows included in this view's activeIndices.
// Performance: O(n) calculation using cached feature distribution + O(n) numerical pass.
// Relationships: Uses GetFeatureDistribution for categorical stats, accesses column for numerical.
// Side effects: May trigger feature distribution caching via GetFeatureDistribution call.
//
// Statistical Calculations:
// - Count/UniqueCount/Mode: Derived from feature distribution
// - Min/Max/Mean: Calculated from numerical values (numerical features only)
// - Missing values excluded from all calculations
//
// Example: stats, err := view.GetFeatureStatistics("age") // Complete statistical summary
func (v *DatasetView[T]) GetFeatureStatistics(featureName string) (*FeatureStats, error) {
	// Get the column to check if it's numerical
	column, err := v.dataset.GetColumn(featureName)
	if err != nil {
		return nil, err
	}

	// Get feature distribution (uses caching)
	distribution, err := v.GetFeatureDistribution(featureName)
	if err != nil {
		return nil, err
	}

	stats := &FeatureStats{
		UniqueCount: len(distribution),
	}

	// Calculate count, mode, and mode count from distribution
	var maxCount int
	var totalCount int
	for value, count := range distribution {
		totalCount += count
		if count > maxCount {
			maxCount = count
			stats.Mode = value
			stats.ModeCount = count
		} else if count == maxCount && count > 0 {
			// Tie for mode - set to nil to indicate ambiguity
			stats.Mode = nil
		}
	}
	stats.Count = totalCount

	// Calculate numerical statistics if this is a numerical column
	if column.IsNumerical() {
		var sum float64
		var min, max *float64
		var numCount int

		for _, physicalIndex := range v.activeIndices {
			value, err := column.GetNumericalValue(physicalIndex)
			if err != nil {
				// Skip missing values
				continue
			}

			sum += value
			numCount++

			if min == nil || value < *min {
				min = &value
			}
			if max == nil || value > *max {
				max = &value
			}
		}

		if numCount > 0 {
			mean := sum / float64(numCount)
			stats.Min = min
			stats.Max = max
			stats.Mean = &mean
		}
	}

	return stats, nil
}

// GetUniqueFeatureValues returns all unique values for a feature within this view.
//
// Args:
// - featureName: Name of feature column (must exist in parent dataset)
//
// Returns: Slice of unique values present in this view (excludes missing values).
// Constraints: Only includes values for rows included in this view's activeIndices.
// Performance: O(n) using cached feature distribution + O(k) slice creation (k = unique count).
// Relationships: Uses GetFeatureDistribution for efficient unique value extraction.
// Side effects: May trigger feature distribution caching via GetFeatureDistribution call.
//
// Order: Values returned in arbitrary order (map iteration order).
// Use case: Categorical feature analysis, split point generation, data exploration.
//
// Example: values, err := view.GetUniqueFeatureValues("department") // ["Engineering", "Sales", "Marketing"]
func (v *DatasetView[T]) GetUniqueFeatureValues(featureName string) ([]any, error) {
	// Get feature distribution (uses caching)
	distribution, err := v.GetFeatureDistribution(featureName)
	if err != nil {
		return nil, err
	}

	// Extract unique values from distribution keys
	uniqueValues := make([]any, 0, len(distribution))
	for value := range distribution {
		uniqueValues = append(uniqueValues, value)
	}

	return uniqueValues, nil
}

// GetFeatureValueCount returns the count of a specific value for a feature within this view.
//
// Args:
// - featureName: Name of feature column (must exist in parent dataset)
// - value: Specific value to count (must match feature's data type)
//
// Returns: Count of occurrences of the specified value in this view.
// Constraints: Only counts for rows included in this view's activeIndices.
// Performance: O(n) first call (triggers distribution caching), O(1) subsequent calls.
// Relationships: Uses GetFeatureDistribution for efficient value lookup.
// Side effects: May trigger feature distribution caching via GetFeatureDistribution call.
//
// Use case: Split evaluation, condition checking, value frequency analysis.
//
// Example: count, err := view.GetFeatureValueCount("department", "Engineering") // returns 15
func (v *DatasetView[T]) GetFeatureValueCount(featureName string, value any) (int, error) {
	// Get feature distribution (uses caching)
	distribution, err := v.GetFeatureDistribution(featureName)
	if err != nil {
		return 0, err
	}

	// Return count for the specific value (0 if not found)
	count, exists := distribution[value]
	if !exists {
		return 0, nil
	}
	return count, nil
}

// CreateChildView creates a new DatasetView from a subset of this view's rows.
//
// Args:
// - logicalIndices: Logical indices within this view (0-based, must be < this.size)
//
// Returns: New DatasetView referencing subset of this view's data.
// Constraints: All logicalIndices must be valid within this view [0, size).
// Performance: O(n) where n = len(logicalIndices) for index translation.
// Relationships: Child view references same parent dataset, different activeIndices.
// Side effects: Allocates new DatasetView, marks target cache as dirty.
//
// Index Translation: logicalIndices -> this.activeIndices[logicalIndices] -> child.activeIndices
// Example: child := view.CreateChildView([]int{0, 2}) // first and third rows of view
func (v *DatasetView[T]) CreateChildView(logicalIndices []int) *DatasetView[T] {
	// Convert logical indices (within this view) to physical indices (in full dataset)
	physicalIndices := make([]int, len(logicalIndices))
	for i, logicalIndex := range logicalIndices {
		physicalIndices[i] = v.activeIndices[logicalIndex]
	}

	return &DatasetView[T]{
		dataset:           v.dataset, // Same full dataset reference
		activeIndices:     physicalIndices,
		size:              len(physicalIndices),
		targetDistDirty:   true,
		featureDists:      make(map[string]map[any]int),
		featureDistsDirty: make(map[string]bool),
	}
}

// GetSize returns the number of active rows in this view.
//
// Returns: Count of rows included in this view (0 for empty view).
// Constraints: Always <= parent dataset's totalSize.
// Performance: O(1) cached value access.
// Relationships: Equals len(activeIndices).
// Side effects: None, read-only operation.
//
// Example: size := view.GetSize() // returns 3 for view with 3 active rows
func (v *DatasetView[T]) GetSize() int {
	return v.size
}

// GetActiveIndices returns a defensive copy of the active indices for this view.
//
// Returns: Copy of physical indices array from parent dataset.
// Constraints: All indices are valid within parent dataset bounds.
// Performance: O(n) where n = number of active indices (creates copy).
// Relationships: Maps to physical row positions in parent dataset.
// Side effects: Allocates new slice, original activeIndices unchanged.
//
// Security: Returns copy to prevent external modification of view's internal state.
// Example: indices := view.GetActiveIndices() // [0, 2, 4] for rows 0, 2, 4
func (v *DatasetView[T]) GetActiveIndices() []int {
	indices := make([]int, len(v.activeIndices))
	copy(indices, v.activeIndices)
	return indices
}
